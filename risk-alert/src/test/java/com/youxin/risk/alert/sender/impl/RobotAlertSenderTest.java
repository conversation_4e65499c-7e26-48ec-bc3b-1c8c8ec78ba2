package com.youxin.risk.alert.sender.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class RobotAlertSenderTest {

    @Resource
    private RobotAlertSender robotAlertSender;

    @Test
    public void testBuildMarkdownMsg() {

    }
}