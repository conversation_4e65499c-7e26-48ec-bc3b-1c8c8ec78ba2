package com.youxin.risk.alert.sender.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.util.concurrent.RateLimiter;
import com.youxin.risk.alert.constants.AlertSourceEnum;
import com.youxin.risk.alert.job.WechatStyleTemplate;
import com.youxin.risk.alert.sender.AlertMessageSplitter;
import com.youxin.risk.alert.sender.AlertSender;
import com.youxin.risk.alert.vo.AlertEvent;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.youxin.risk.commons.utils.JsonFormatUtil.formatWechatStr;


@Service("RobotAlertSender")
public class RobotAlertSender implements AlertSender {

    @Value("${wechat.robot.url}")
    private String wechatRobotUrl;

    private final RobotAlterMessageSplitter messageSplitter = new RobotAlterMessageSplitter();

    private Logger logger = LoggerFactory.getLogger(WechatAlertSender.class);

    private HashMap<String, RateLimiter> robotKeyRateMap = new HashMap<>();
    private double limitPerSec = 0.1;//发送企业微信的频率(官方20/min,此处报警发送不要求太严时效暂定6/min)

    @Override
    public void send(AlertEvent alertEvent) {
        //企业微信机器人发消息
        sendWechatRobot(alertEvent);
        LoggerProxy.info("notifyAlertWechatRobot", logger, "alertId={}，policyName:{}",
                alertEvent.getAlertId(),alertEvent.getAlertPolicy().getPolicyName());
    }

    private void sendWechatRobot(AlertEvent alertEvent) {
        int retryCount = 3;
        while (retryCount > 0){
            try{
                LoggerProxy.info("send alert to wechat", logger, "policyName:{}",
                        alertEvent.getAlertPolicy().getPolicyName());
                sendRobotMsg(alertEvent);
                break;
            }catch (Exception e){
                retryCount --;
                if(retryCount == 0){
                    logger.error("sendWechatRobotError", e);
                }
                sleep();
            }
        }
    }

    private void sleep(){
        try {
            Thread.sleep(1000L);
        } catch (InterruptedException ex) {
        }
    }

    private void sendRobotMsg(AlertEvent alertEvent) {
        String robotKey = alertEvent.getAlertPolicy().getRobotKey();
        logger.info("before sendRobotMsg,robotKey:{},{}", robotKey, JSON.toJSONString(alertEvent));
        RateLimiter rateLimiter = robotKeyRateMap.get(robotKey);
        if (rateLimiter == null) {
            rateLimiter = RateLimiter.create(limitPerSec);
            robotKeyRateMap.put(robotKey, rateLimiter);
        }
        double acquireCostTime = rateLimiter.acquire(1);
        String sendUrl = wechatRobotUrl + robotKey;


        if (alertEvent.getMsgType().equals(AlertEvent.MsgTypeCustom)) {
            doSend(JSON.parseObject(alertEvent.getMessage()), sendUrl, acquireCostTime);
        } else if (alertEvent.getMsgType().equals(AlertEvent.MsgTypeText)
                && alertEvent.getAlertSource() == AlertSourceEnum.riskAlert
        && (alertEvent.getAlertPolicy().getPolicyName().contains("strategy_code_online_notice")
                || alertEvent.getAlertPolicy().getPolicyName().contains("rule_key_bind_notice")
                || alertEvent.getAlertPolicy().getPolicyName().contains("rule_key_unbind_notice")
                || alertEvent.getAlertPolicy().getPolicyName().contains("rule_key_online_notice")
        )) { // todo后续前端支持选择格式
            List<Map<String, Object>> paramList = new ArrayList<>();
            String markdownMsg = buildMarkdownMsg(alertEvent);  // 构建Markdown消息内容
            if (messageSplitter.evaluate(markdownMsg)) {
                // markdown超长，按text发送
                List<String> split = messageSplitter.split(alertEvent.getMessage());
                split.forEach(s -> {
                    alertEvent.setMessage(s);
                    paramList.add(buildSingle(alertEvent));
                });
            } else {
                AlertEvent markdownAlertEvent = new AlertEvent(alertEvent.getAlertSource(), markdownMsg);
                paramList.add(buildMdSingle(markdownAlertEvent));
            }

            paramList.forEach(s -> doSend(s, sendUrl, acquireCostTime));
        } else {
            List<Map<String, Object>> paramList = new ArrayList<>();
            if (messageSplitter.evaluate(alertEvent.getMessage())) {
                List<String> split = messageSplitter.split(alertEvent.getMessage());
                split.forEach(s -> {
                    alertEvent.setMessage(s);
                    paramList.add(buildSingle(alertEvent));
                });
            } else {
                paramList.add(buildSingle(alertEvent));
            }

            paramList.forEach(s -> doSend(s, sendUrl, acquireCostTime));
        }
    }

    private void doSend(Map<String,Object> params, String sendUrl, double acquireCostTime) {
        Map<String,String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        logger.info("sendWechatRobot,sendBody={},acquireCostTime={}", JSON.toJSONString(params), acquireCostTime);
        String ret = SyncHTTPRemoteAPI.postJson(sendUrl, JSON.toJSONString(params), header, 30000, false);
        logger.info("sendWechatRobotEnd,ret={}", ret);
    }

    private Map<String,Object> buildSingle(AlertEvent alertEvent) {
        Map<String,Object> params = new HashMap<>();
        JSONObject alertContent = new JSONObject();
        alertContent.put("content", buildContent(alertEvent));
        params.put("msgtype", alertEvent.getMsgType());
        params.put(alertEvent.getMsgType(), alertContent);
        return params;
    }

    /**
     * 构建Markdown消息
     * @param alertEvent
     * @return
     */
    private Map<String, Object> buildMdSingle(AlertEvent alertEvent) {
        Map<String, Object> params = new HashMap<>();
        JSONObject markdown = new JSONObject();
        markdown.put("content", alertEvent.getMessage());
        params.put("msgtype", "markdown");
        params.put("markdown", markdown);
        return params;
    }


    /**
     * 构建 Markdown 消息内容
     *
     * @param alertEvent 告警事件
     * @return Markdown 格式的消息内容
     */
    private String buildMarkdownMsg(AlertEvent alertEvent) {
        try {
            JSONObject jsonObject = JSON.parseObject(alertEvent.getMessage(), JSONObject.class, Feature.OrderedField);
            StringBuilder markdownContent = new StringBuilder();
            markdownContent.append(String.format("# %s  \n\n", alertEvent.getAlertPolicy().getPolicyDesc()));  // 使用更大的标题

            for (String key : jsonObject.keySet()) {
                Object value = jsonObject.get(key);
                markdownContent.append("> **").append(key).append(": ** ")
                        .append(infoMessage(String.valueOf(value))).append("  \n");
            }
            return markdownContent.toString();
        } catch (Exception e) {
            logger.error("构建 Markdown 消息失败：{}", e.getMessage(), e);
            return "构建 Markdown 消息失败，请查看日志";
        }
    }

    /**
     * 格式化信息消息
     * @param message 消息
     * @return 格式化后的消息
     */
    protected String infoMessage(String message) {
        return String.format("<font color=\"info\">%s</font>", message);
    }

    private String buildContent(AlertEvent alertEvent) {
        if(StringUtils.isEmpty(alertEvent.getTitle())){
            return formatWechatStr(alertEvent.getMessage());
        }else if(alertEvent.getAlertPolicy().getPolicyName().endsWith("_JsonAryTmp")){//ui暂未新增模板字段选项暂通过策略名后缀判断
            return WechatStyleTemplate.getSigleLineTempByJsonAry(alertEvent.getMessage());
        }else{
            return alertEvent.getTitle() + "\n" + formatWechatStr(alertEvent.getMessage());
        }
    }


    static class RobotAlterMessageSplitter implements AlertMessageSplitter<String> {
        private static final Logger log = LoggerFactory.getLogger(RobotAlterMessageSplitter.class);
        private static final int MAX_CONTENT_SIZE = 4500;
        @Override
        public boolean evaluate(String alertContent) {
            if (StringUtils.isEmpty(alertContent)) {
                return false;
            }
            // text.content exceed max length 5120
            return alertContent.getBytes(StandardCharsets.UTF_8).length > MAX_CONTENT_SIZE;
        }

        @Override
        public List<String> split(String alertContent) {
            log.info("RobotAlterMessageSplitter send content exceed {} start do split ", MAX_CONTENT_SIZE);
            byte[] contentBytes = alertContent.getBytes(StandardCharsets.UTF_8);
            int ceil = (int)Math.ceil(contentBytes.length / MAX_CONTENT_SIZE);
            List<String> result = new ArrayList<>();
            for (int i = 0; i < ceil; i++) {
                byte[] bytes = Arrays.copyOfRange(contentBytes, i * MAX_CONTENT_SIZE, i * MAX_CONTENT_SIZE + MAX_CONTENT_SIZE);
                result.add(new String(bytes));
            }
            return result;
        }
    }
}
