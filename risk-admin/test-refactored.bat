@echo off
echo 正在测试重构后的测试类...
echo.

echo 1. 编译测试类...
call mvn test-compile -q
if %errorlevel% neq 0 (
    echo 编译失败！
    exit /b 1
)

echo 2. 运行 StrategyMonitorConfigServiceTest...
call mvn test -Dtest=StrategyMonitorConfigServiceTest -q
if %errorlevel% neq 0 (
    echo StrategyMonitorConfigServiceTest 测试失败！
    exit /b 1
)

echo 3. 运行 SLSLinkGeneratorUnifiedConfigTest...
call mvn test -Dtest=SLSLinkGeneratorUnifiedConfigTest -q
if %errorlevel% neq 0 (
    echo SLSLinkGeneratorUnifiedConfigTest 测试失败！
    exit /b 1
)

echo.
echo 所有测试都通过了！重构成功！
