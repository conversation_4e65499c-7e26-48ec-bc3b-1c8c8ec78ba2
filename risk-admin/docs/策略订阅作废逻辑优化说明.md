# 策略订阅作废逻辑优化说明

## 概述

本文档描述了对策略监控订阅服务中"相同策略前后间隔很近时的订阅作废逻辑"的优化实现。

## 问题背景

### 原始问题
当相同策略前后间隔很近，前边的订阅通知还没有发送完，后边的相同策略代码上线时，需要让之前的订阅作废，生成新的订阅记录。

### 业务场景
1. **策略快速迭代**：开发人员在短时间内多次上线同一策略
2. **推送未完成**：前一次上线的订阅推送还未达到设定次数（默认3次）
3. **避免重复推送**：防止用户收到过多的监控链接推送

## 解决方案

### 1. 核心逻辑优化

#### 原始逻辑（逐个检查）
```java
// 原始逻辑：逐个检查每个监控项的订阅记录
for (MonitorConfig monitor : mapping.getMonitors()) {
    SubscriptionRecord existingRecord = subscriptionRecordMapper.findByUserAndStrategyAndMonitor(
            userId, strategyType, monitor.getId());
    
    if (existingRecord != null && existingRecord.getSentPushes() < existingRecord.getTotalPushes()) {
        // 逐个更新每条记录
        existingRecord.setIsActive(0);
        subscriptionRecordMapper.update(existingRecord);
    }
}
```

#### 优化后逻辑（批量处理）
```java
// 优化逻辑：批量作废该用户该策略类型下所有未完成的订阅记录
int deactivatedCount = subscriptionRecordMapper.batchDeactivateUnfinishedSubscriptions(userId, strategyType);
if (deactivatedCount > 0) {
    LoggerProxy.info("createStrategySubscription", logger,
            "批量作废未完成的订阅记录, userId={}, strategyType={}, deactivatedCount={}",
            userId, strategyType, deactivatedCount);
}
```

### 2. 数据库层面优化

#### 新增Mapper方法
```java
/**
 * 批量作废用户策略的未完成订阅记录
 * 
 * @param userId 用户ID
 * @param strategyType 策略类型
 * @return 更新的记录数
 */
int batchDeactivateUnfinishedSubscriptions(@Param("userId") String userId, 
                                          @Param("strategyType") String strategyType);
```

#### SQL实现
```sql
UPDATE t_subscription_record
SET 
    is_active = 0,
    update_time = CURRENT_TIMESTAMP
WHERE user_id = #{userId}
AND strategy_type = #{strategyType}
AND is_active = 1
AND sent_pushes < total_pushes
```

### 3. 服务层面优化

#### 新增服务方法
```java
/**
 * 批量作废用户策略的未完成订阅记录
 * 
 * @param userId 用户ID
 * @param strategyType 策略类型
 * @return 更新的记录数
 */
public int batchDeactivateUnfinishedSubscriptions(String userId, String strategyType)
```

## 优化效果

### 1. 性能提升
- **减少数据库查询**：从N次查询+N次更新 → 1次批量更新
- **减少网络开销**：批量操作减少数据库连接次数
- **提高并发性能**：减少数据库锁定时间

### 2. 逻辑简化
- **统一处理**：不再需要逐个检查每个监控项
- **原子操作**：整个作废操作在一个SQL事务中完成
- **减少代码复杂度**：简化了业务逻辑

### 3. 可维护性提升
- **清晰的日志**：明确记录作废了多少条记录
- **统一的异常处理**：集中处理批量操作的异常
- **易于测试**：独立的方法便于单元测试

## 实现细节

### 1. 作废条件
满足以下所有条件的订阅记录会被作废：
- `user_id = #{userId}` - 指定用户
- `strategy_type = #{strategyType}` - 指定策略类型
- `is_active = 1` - 当前激活状态
- `sent_pushes < total_pushes` - 推送未完成

### 2. 执行时机
在创建新订阅记录之前，先执行批量作废操作：
```java
// 先批量作废该用户该策略类型下所有未完成的订阅记录
int deactivatedCount = subscriptionRecordMapper.batchDeactivateUnfinishedSubscriptions(userId, strategyType);

// 然后创建新的订阅记录
for (MonitorConfig monitor : mapping.getMonitors()) {
    // 创建新订阅记录的逻辑
}
```

### 3. 日志记录
- **作废操作日志**：记录作废的记录数量
- **创建操作日志**：记录新创建的订阅记录
- **异常处理日志**：记录操作失败的详细信息

## 测试验证

### 1. 单元测试
创建了专门的测试类 `StrategySubscriptionServiceDeactivationTest`：
- 测试批量作废功能
- 测试创建订阅时的作废逻辑
- 测试边界条件和异常处理

### 2. 集成测试
- 验证与数据库的交互
- 验证与Spring容器的集成
- 验证日志输出的正确性

## 兼容性说明

### 1. 向后兼容
- 保持原有API接口不变
- 保持数据库表结构不变
- 保持业务逻辑语义不变

### 2. 配置兼容
- 无需修改现有配置
- 无需修改调用方代码
- 无需数据迁移

## 总结

通过这次优化，我们实现了：

1. **性能优化**：从O(N)的复杂度优化到O(1)
2. **逻辑简化**：减少了代码复杂度和维护成本
3. **功能增强**：提供了更好的用户体验和系统稳定性
4. **可维护性**：增加了清晰的日志和测试覆盖

这个优化确保了在策略快速迭代的场景下，用户不会收到过多的重复推送，同时保持了系统的高性能和稳定性。
