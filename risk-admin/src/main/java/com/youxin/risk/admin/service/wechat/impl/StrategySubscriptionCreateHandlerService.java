package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SubscriptionUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 创建策略监控订阅处理服务
 */
@Service
public class StrategySubscriptionCreateHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            String userId = getRequiredParam(params, "userId");
            String strategyType = getRequiredParam(params, "strategyType");

            LoggerProxy.info("processMessage", logger, 
                    "处理创建订阅请求, userId={}, strategyType={}", userId, strategyType);

            // 参数验证
            if (!SubscriptionUtils.areAllNotEmpty(userId, strategyType)) {
                throw new IllegalArgumentException("用户ID和策略类型不能为空");
            }

            // 检查策略类型是否已配置
            if (!strategyMonitorConfigService.isStrategyTypeConfigured(strategyType)) {
                throw new IllegalArgumentException("策略类型未配置监控项: " + strategyType);
            }

            // 创建订阅
            boolean success = strategySubscriptionService.createStrategySubscription(userId, strategyType);
            
            if (success) {
                LoggerProxy.info("processMessage", logger, 
                        "创建订阅成功, userId={}, strategyType={}", userId, strategyType);
                return true;
            } else {
                LoggerProxy.warn("processMessage", logger, 
                        "创建订阅失败, userId={}, strategyType={}", userId, strategyType);
                return false;
            }

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger, 
                    "创建订阅异常, params=" + params, e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 创建策略监控订阅\n\n");
        
        String userId = getParam(params, "userId");
        String strategyType = getParam(params, "strategyType");
        
        markdownContent.append("> **用户ID:** ").append(infoMessage(userId)).append("\n");
        markdownContent.append("> **策略类型:** ").append(infoMessage(strategyType)).append("\n");
        
        if (result instanceof Boolean) {
            boolean success = (Boolean) result;
            if (success) {
                markdownContent.append("> **操作结果:** ").append(successMessage("订阅创建成功")).append("\n");
                markdownContent.append("\n> 系统将开始为您推送该策略的监控信息。");
            } else {
                markdownContent.append("> **操作结果:** ").append(warnMessage("订阅创建失败")).append("\n");
            }
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("创建异常")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_CREATE.getCode();
    }
}
