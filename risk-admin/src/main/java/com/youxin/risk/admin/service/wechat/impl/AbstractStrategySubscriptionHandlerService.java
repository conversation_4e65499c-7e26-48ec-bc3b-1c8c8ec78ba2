package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.admin.service.subscription.StrategyMonitorConfigService;
import com.youxin.risk.admin.service.subscription.StrategySubscriptionService;
import com.youxin.risk.admin.service.wechat.AbstractWeChatHandlerService;
import com.youxin.risk.admin.service.wechat.WechatMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

/**
 * 策略订阅处理服务抽象基类
 */
public abstract class AbstractStrategySubscriptionHandlerService extends AbstractWeChatHandlerService<Object> {

    @Autowired
    protected StrategySubscriptionService strategySubscriptionService;

    @Autowired
    protected StrategyMonitorConfigService strategyMonitorConfigService;

    @Autowired
    protected WechatMessageService wechatMessageService;

    @Override
    protected String getTargetUserId(Map<String, String> params) {
        // 策略订阅相关的消息都发送给指定用户
        String userId = getParam(params, "userId");
        if (userId == null || userId.trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userId;
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 策略订阅操作结果\n\n");
        
        if (result instanceof Boolean) {
            boolean success = (Boolean) result;
            if (success) {
                markdownContent.append("> **操作结果:** ").append(successMessage("成功")).append("\n");
            } else {
                markdownContent.append("> **操作结果:** ").append(warnMessage("失败")).append("\n");
            }
        } else if (result instanceof String) {
            markdownContent.append("> **操作结果:** ").append(infoMessage((String) result)).append("\n");
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("异常")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        } else {
            markdownContent.append("> **操作结果:** ").append(infoMessage("已完成")).append("\n");
            if (result != null) {
                markdownContent.append("> **详细信息:** ").append(result.toString()).append("\n");
            }
        }
        
        return markdownContent.toString();
    }

    /**
     * 格式化成功消息
     * @param message 消息
     * @return 格式化后的消息
     */
    protected String successMessage(String message) {
        return String.format("<font color=\"info\">%s</font>", message);
    }

    /**
     * 获取参数值
     * @param params 参数映射
     * @param key 参数键
     * @return 参数值
     */
    protected String getParam(Map<String, String> params, String key) {
        return params.get(key);
    }

    /**
     * 获取必需参数值
     * @param params 参数映射
     * @param key 参数键
     * @return 参数值
     * @throws IllegalArgumentException 如果参数为空
     */
    protected String getRequiredParam(Map<String, String> params, String key) {
        String value = params.get(key);
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("参数 " + key + " 不能为空");
        }
        return value;
    }
}
