package com.youxin.risk.admin.service.wechat;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.admin.tools.wechat.QwClient;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.google.common.base.Stopwatch;

/**
 * @desc 企业微信消息处理抽象服务
 */
public abstract class AbstractWeChatHandlerService<T> {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private QwClient qwClient;

    /**
     * 获取目标用户ID（企业微信应用消息）
     * @param params 参数
     * @return 用户ID，必须返回有效的用户ID
     */
    protected abstract String getTargetUserId(Map<String, String> params);

    /**
     * 处理消息
     * @param params 参数
     */
    public void handler(Map<String, String> params) {
        LoggerProxy.info("企业微信消息处理", logger, "开始，消息类型={},params={}", supportMsgType(), params);
        Stopwatch stopwatch = Stopwatch.createStarted();
        T t = processMessage(params);
        LoggerProxy.info("企业微信消息处理", logger, "处理消息结果={}，处理耗时={}ms", JSON.toJSONString(t),
                stopwatch.elapsed(TimeUnit.MILLISECONDS));

        String responseMessage = buildResponseMessage(t, params);
        LoggerProxy.info("企业微信消息处理", logger, "构建好的响应消息={}", responseMessage);

        sendMessage(responseMessage, params);
        LoggerProxy.info("企业微信消息处理", logger, "发送响应消息结束");
    }

    /**
     * 处理消息
     * @param params 参数
     * @return 处理结果
     */
    protected abstract T processMessage(Map<String, String> params);

    /**
     * 构建响应消息
     * @param t 处理结果
     * @param params 参数
     * @return 响应消息
     */
    protected abstract String buildResponseMessage(T t, Map<String, String> params);

    /**
     * 支持的消息类型
     * @return 消息类型
     */
    protected abstract String supportMsgType();

    /**
     * 发送消息（兼容旧版本）
     * @param msg 消息内容
     */
    protected void sendMessage(String msg) {
        sendMessage(msg, null);
    }

    /**
     * 发送消息
     * @param msg 消息内容
     * @param params 参数
     */
    protected void sendMessage(String msg, Map<String, String> params) {
        if (msg == null) {
            LoggerProxy.warn("发送企微消息", logger, "发送消息体为空，不执行发送操作");
            return;
        }

        if (msg.length() > 4000) {
            LoggerProxy.warn("发送企微消息", logger, "消息长度超过4000字节，执行截断操作，截断前消息为={}", msg);
            msg = msg.substring(0, 4000);
        }

        // 获取目标用户ID并发送到企业微信应用
        String targetUserId = params != null ? getTargetUserId(params) : null;

        if (targetUserId != null && !targetUserId.trim().isEmpty()) {
            qwClient.sendMarkdownMessage(targetUserId, msg);
        } else {
            LoggerProxy.error("sendMessage", logger, "无法获取有效的用户ID，消息发送失败");
            throw new RuntimeException("无法获取有效的用户ID，消息发送失败");
        }
    }

    /**
     * 格式化警告消息
     * @param message 消息
     * @return 格式化后的消息
     */
    protected String warnMessage(String message) {
        return String.format("<font color=\"warning\">%s</font>", message);
    }

    /**
     * 格式化信息消息
     * @param message 消息
     * @return 格式化后的消息
     */
    protected String infoMessage(String message) {
        return String.format("<font color=\"info\">%s</font>", message);
    }
}