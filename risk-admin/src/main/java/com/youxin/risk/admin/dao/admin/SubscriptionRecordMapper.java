package com.youxin.risk.admin.dao.admin;

import com.youxin.risk.admin.dao.BaseMapper;
import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 策略监控订阅记录 Mapper 接口
 * 

 */
public interface SubscriptionRecordMapper extends BaseMapper<SubscriptionRecord> {

    /**
     * 查询需要推送的订阅记录
     * 
     * @return 需要推送的订阅记录列表
     */
    List<SubscriptionRecord> findPendingSubscriptions();

    /**
     * 批量更新推送状态
     * 
     * @param ids 记录ID列表
     * @return 更新的记录数
     */
    int batchUpdatePushStatus(@Param("ids") List<Long> ids);

    /**
     * 根据用户ID、策略类型和监控ID查询订阅记录
     * 
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @param monitorId 监控ID
     * @return 订阅记录
     */
    SubscriptionRecord findByUserAndStrategyAndMonitor(@Param("userId") String userId, 
                                                      @Param("strategyType") String strategyType, 
                                                      @Param("monitorId") String monitorId);

    /**
     * 根据用户ID和策略类型查询订阅记录
     * 
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @return 订阅记录列表
     */
    List<SubscriptionRecord> findByUserAndStrategy(@Param("userId") String userId, 
                                                  @Param("strategyType") String strategyType);

    /**
     * 批量插入订阅记录
     *
     * @param records 订阅记录列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("records") List<SubscriptionRecord> records);

    /**
     * 批量作废用户策略的未完成订阅记录
     *
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @return 更新的记录数
     */
    int batchDeactivateUnfinishedSubscriptions(@Param("userId") String userId,
                                              @Param("strategyType") String strategyType);
}
