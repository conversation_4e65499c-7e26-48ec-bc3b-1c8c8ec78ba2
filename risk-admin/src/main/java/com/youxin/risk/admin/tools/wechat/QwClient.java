package com.youxin.risk.admin.tools.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.yonxin.risk.approve.client.model.TemplateCreateResp;
import com.youxin.risk.admin.tools.wechat.common.QwEnum;
import com.youxin.risk.admin.tools.wechat.model.QwAccessTokenVo;
import com.youxin.risk.admin.tools.wechat.model.QwBaseVo;
import com.youxin.risk.admin.tools.wechat.model.QwUserInfoVo;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class QwClient {
    private final static Logger LOGGER = LoggerFactory.getLogger(QwClient.class);

    private final String GET_TOKEN_URL = "/gettoken";
    private final String GET_USER_INFO = "/user/get";
    private final String CREATE_TEMPLATE_URL = "/oa/approval/create_template";
    private final String SEND_MESSAGE_URL = "/message/send";

    /**
     * HTTP请求超时时间（毫秒）
     */
    private static final int HTTP_TIMEOUT = 10000;

    /**
     * 企业微信地址
     */
    @Value(value = "${qw.url:https://qyapi.weixin.qq.com/cgi-bin}")
    private String qwUrl;

    /**
     * 企业微信应用ID
     */
    @Value("${qw.agent.id:1000146}")
    private Integer agentId;


    public QwUserInfoVo getUserByUserId(String userId) {
        String accessToken = getAccessToken();
        return getUserByUserId(userId, accessToken);
    }

    /**
     * 获取企业微信用户详情
     * https://developer.work.weixin.qq.com/document/path/90196
     * @param userId 成员UserID。对应管理端的帐号，企业内必须唯一
     * @param accessToken 调用接口凭证
     * @return
     */
    public QwUserInfoVo getUserByUserId(String userId, String accessToken) {
        String getUserInfoUrl = String.format(qwUrl + GET_USER_INFO + "?access_token=%s&userid=%s", accessToken, userId);
        String response = SyncHTTPRemoteAPI.get(getUserInfoUrl, HTTP_TIMEOUT);
        LOGGER.info("获取用户信息完成, url={}, response={}", getUserInfoUrl, response);
        QwUserInfoVo qwUserInfoVo = JSON.parseObject(response, QwUserInfoVo.class);
        if (QwBaseVo.SUCCESS != qwUserInfoVo.getErrCode()) {
            return null;
        }
        return qwUserInfoVo;
    }

    /**
     * 根据企业环境获取access_token
     * https://developer.work.weixin.qq.com/document/path/91039
     * @param enterpriseEnv 企业环境
     * @return access_token
     */
    public String getAccessToken(String enterpriseEnv) {
        QwEnum qwEnum = QwEnum.getByEnterpriseEnv(enterpriseEnv);
        if (envAndAccessTokenMap.containsKey(qwEnum)) {
            return envAndAccessTokenMap.get(qwEnum);
        }

        String getTokenUrl = String.format(qwUrl + GET_TOKEN_URL + "?corpid=%s&corpsecret=%s", qwEnum.getCorpId(), qwEnum.getCorpSecret());
        String response = SyncHTTPRemoteAPI.get(getTokenUrl, HTTP_TIMEOUT);
        LOGGER.info("获取access_token完成, url={}, response={}", getTokenUrl, JSON.toJSONString(response));
        QwAccessTokenVo accessTokenVo = JSON.parseObject(response, QwAccessTokenVo.class);
        if (QwBaseVo.SUCCESS != accessTokenVo.getErrCode()) {
            return null;
        }
        envAndAccessTokenMap.put(qwEnum, accessTokenVo.getAccessToken());
        return accessTokenVo.getAccessToken();
    }

    /**
     * 获取access_token
     * https://developer.work.weixin.qq.com/document/path/91039
     * @return access_token
     */
    public String getAccessToken() {
        return getAccessToken(null);
    }

    /**
     * 发送企业微信应用消息（Markdown格式）
     * https://developer.work.weixin.qq.com/document/path/90236
     *
     * @param userId 用户ID
     * @param content 消息内容（Markdown格式）
     * @return 是否发送成功
     */
    public boolean sendMarkdownMessage(String userId, String content) {
        return sendMarkdownMessage(userId, content, agentId);
    }

    /**
     * 发送企业微信应用消息（Markdown格式）
     * https://developer.work.weixin.qq.com/document/path/90236
     *
     * @param userId 用户ID
     * @param content 消息内容（Markdown格式）
     * @param agentId 应用ID
     * @return 是否发送成功
     */
    public boolean sendMarkdownMessage(String userId, String content, Integer agentId) {
        // 参数验证
        if (userId == null || userId.trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("消息内容不能为空");
        }
        if (agentId == null) {
            throw new IllegalArgumentException("应用ID不能为空");
        }

        try {
            LoggerProxy.info("sendMarkdownMessage", LOGGER,
                    "开始发送企业微信应用消息, userId={}, agentId={}, contentLength={}",
                    userId, agentId, content.length());

            String accessToken = getAccessToken();
            if (accessToken == null || accessToken.trim().isEmpty()) {
                LoggerProxy.error("sendMarkdownMessage", LOGGER, "获取企业微信access_token失败");
                throw new RuntimeException("获取企业微信access_token失败");
            }

            // 构建请求体
            JSONObject message = new JSONObject();
            message.put("touser", userId);
            message.put("msgtype", "markdown");
            message.put("agentid", agentId);

            JSONObject markdown = new JSONObject();
            markdown.put("content", content);
            message.put("markdown", markdown);

            // 发送消息
            String url = qwUrl + SEND_MESSAGE_URL + "?access_token=" + accessToken;
            String response = SyncHTTPRemoteAPI.postJson(url, message.toJSONString(), HTTP_TIMEOUT);

            LoggerProxy.info("sendMarkdownMessage", LOGGER,
                    "企业微信应用消息发送请求完成, userId={}, agentId={}, response={}", userId, agentId, response);

            // 检查响应
            if (response == null || response.trim().isEmpty()) {
                LoggerProxy.error("sendMarkdownMessage", LOGGER, "企业微信API响应为空");
                throw new RuntimeException("企业微信API响应为空");
            }

            JSONObject responseJson = JSONObject.parseObject(response);
            int errcode = responseJson.getIntValue("errcode");
            if (errcode != 0) {
                String errmsg = responseJson.getString("errmsg");
                LoggerProxy.error("sendMarkdownMessage", LOGGER,
                        "企业微信应用消息发送失败, userId={}, errcode={}, errmsg={}", userId, errcode, errmsg);
                throw new RuntimeException(String.format("企业微信应用消息发送失败[%d]: %s", errcode, errmsg));
            }

            LoggerProxy.info("sendMarkdownMessage", LOGGER,
                    "企业微信应用消息发送成功, userId={}, agentId={}", userId, agentId);
            return true;

        } catch (Exception e) {
            LoggerProxy.error("sendMarkdownMessage", LOGGER,
                    "发送企业微信应用消息异常, userId={}, agentId={}", userId, agentId, e);
            if (e instanceof RuntimeException) {
                throw e;
            }
            throw new RuntimeException("发送企业微信应用消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 每10分钟刷新一下token
     */
    @Scheduled(cron = "* */10 * * * ?")
    public void clearAccessTokenMap() {
        envAndAccessTokenMap.clear();
    }

    private Map<QwEnum, String> envAndAccessTokenMap = new HashMap<>();

    /**
     * 获取审批申请详情
     * https://developer.work.weixin.qq.com/document/path/91983
     * @param requestParam 审批模板参数
     * @return 审批申请详情
     */
    public TemplateCreateResp createApproveTemplate(JSONObject requestParam) {
        String accessToken = getAccessToken();
        String createApproveTemplateUrl = String.format(qwUrl + CREATE_TEMPLATE_URL + "?access_token=%s", accessToken);
        String response = SyncHTTPRemoteAPI.postJson(createApproveTemplateUrl, requestParam.toJSONString(), HTTP_TIMEOUT);
        LOGGER.info("创建审批模板, url={}, body={}, response={}", createApproveTemplateUrl, JSON.toJSONString(requestParam),
                response);

        TemplateCreateResp templateCreateResp = JSON.parseObject(response, TemplateCreateResp.class);
        if (QwBaseVo.SUCCESS != templateCreateResp.getErrCode()) {
            throw new RuntimeException(templateCreateResp.getErrMsg());
        }
        return templateCreateResp;
    }

    private final String DEPARTMENT_LIST = "/department/list";

    public JSONArray queryAllDepartments() {
        String accessToken = getAccessToken();
        String getDepartmentListUrl = String.format(qwUrl + DEPARTMENT_LIST + "?access_token=%s&id=1", accessToken);
        String responseStr = SyncHTTPRemoteAPI.get(getDepartmentListUrl, HTTP_TIMEOUT);
        JSONObject response = JSONObject.parseObject(responseStr);
        if (QwBaseVo.SUCCESS != response.getIntValue("errcode")) {
            String errMsg = String.format("获取部门信息异常，异常原因=%s", response.getString("errmsg"));
            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
        }
        return response.getJSONArray("department");
    }
    private final String USER_SIMPLE_LIST = "/user/simplelist";
    public JSONArray queryMembersByDepartmentId(long departmentId) {
        String accessToken = getAccessToken();
        String getDepartmentListUrl = String.format(qwUrl + USER_SIMPLE_LIST + "?access_token=%s&department_id=%S",
                accessToken, departmentId);
        Stopwatch stopwatch = Stopwatch.createStarted();
        String responseStr = SyncHTTPRemoteAPI.get(getDepartmentListUrl, HTTP_TIMEOUT);
        JSONObject response = JSONObject.parseObject(responseStr);
        LOGGER.info("根据部门id获取成员信息，部门id={},返回结果={}，耗时={}ms", departmentId, response.toJSONString(),
                stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (QwBaseVo.SUCCESS != response.getIntValue("errcode")) {
            String errMsg = String.format("根据部门id获取成员信息异常，异常原因=%s", response.getString("errmsg"));
            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
        }
        return response.getJSONArray("userlist");
    }

    private final String USER_SIMPLE_GET = "/user/get";
    public JSONObject queryMembers(String userId) {
        String accessToken = getAccessToken();
        //https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&userid=USERID
        String queryMembersUrl = String.format(qwUrl + USER_SIMPLE_GET + "?access_token=%s&userid=%S",accessToken, userId);
        Stopwatch stopwatch = Stopwatch.createStarted();
        String responseStr = SyncHTTPRemoteAPI.get(queryMembersUrl, HTTP_TIMEOUT);
        JSONObject response = JSONObject.parseObject(responseStr);
        LOGGER.info("根据用户id获取成员信息，用户id={},返回结果={}，耗时={}ms", userId, response.toJSONString(),stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (QwBaseVo.SUCCESS != response.getIntValue("errcode")) {
            String errMsg = String.format("根据用户id获取成员信息，异常原因=%s", response.getString("errmsg"));
            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
        }

        JSONObject childDepartment = getChildDepartment(1000004164L);//策略部
        LOGGER.info("childDepartment={}", JSON.toJSONString(childDepartment));

        JSONArray array = childDepartment.getJSONArray("department_id");
        Set<Long> deptIds = new HashSet<>();
        for (int i = 0 ; i < array.size() ; i++){
            JSONObject jsonObject = array.getJSONObject(i);
            deptIds.add(jsonObject.getLong("id"));
        }

        LOGGER.info("deptIds={}", JSON.toJSONString(deptIds));
        Long deptId = response.getLong("main_department");
        JSONObject isStrategy = new JSONObject();
        if (deptIds.contains(deptId)){
            isStrategy.put("isStrategy",true);
        }else {
            isStrategy.put("isStrategy",false);
        }
        return isStrategy;
    }

    private final String DEPARTMENT_SIMPLE_GET = "/department/get";
    public JSONObject queryDepartment(Long deptId) {
        String accessToken = getAccessToken();
        String queryDeptUrl = String.format(qwUrl + DEPARTMENT_SIMPLE_GET + "?access_token=%s&id=%S",accessToken, deptId);
        Stopwatch stopwatch = Stopwatch.createStarted();
        String responseStr = SyncHTTPRemoteAPI.get(queryDeptUrl, HTTP_TIMEOUT);
        JSONObject response = JSONObject.parseObject(responseStr);
        LOGGER.info("根据部门id获取部门信息，部门id={},返回结果={}，耗时={}ms", deptId, response.toJSONString(),stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (QwBaseVo.SUCCESS != response.getIntValue("errcode")) {
            String errMsg = String.format("根据用户id获取部门信息，异常原因=%s", response.getString("errmsg"));
            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
        }
        return response;
    }

    private final String SIMPLE_LIST = "/department/simplelist";
    public JSONObject getChildDepartment(Long deptId) {
        //https://qyapi.weixin.qq.com/cgi-bin/department/simplelist?access_token=ACCESS_TOKEN&id=ID
        String accessToken = getAccessToken();
        String queryDeptUrl = String.format(qwUrl + SIMPLE_LIST + "?access_token=%s&id=%S",accessToken, deptId);
        Stopwatch stopwatch = Stopwatch.createStarted();
        LOGGER.info("getChildDepartment，queryDeptUrl={}", deptId, queryDeptUrl);

        String responseStr = SyncHTTPRemoteAPI.get(queryDeptUrl, HTTP_TIMEOUT);
        JSONObject response = JSONObject.parseObject(responseStr);
        LOGGER.info("根据部门id获取子部门信息，部门id={},返回结果={}，耗时={}ms", deptId, response.toJSONString(),stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (QwBaseVo.SUCCESS != response.getIntValue("errcode")) {
            String errMsg = String.format("根据用户id获取子部门信息，异常原因=%s", response.getString("errmsg"));
            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
        }
        return response;
    }


    public JSONObject queryAllMembersForTree() {
        return queryAllMembersForTree(null);
    }

    public JSONObject queryAllMembersForTree(String enterpriseEnv) {
        QwEnum qwEnum = QwEnum.getByEnterpriseEnv(enterpriseEnv);
        if (envAndAllMembers.containsKey(qwEnum)) {
            return envAndAllMembers.get(qwEnum);
        }

        JSONArray departments = queryAllDepartments();
        JSONObject maxDepartment = null;
        for (int i = 0; i < departments.size(); i++) {
            JSONObject department = departments.getJSONObject(i);
            long id = department.getLongValue("id");
            if (id == 1) {
                maxDepartment = department;
            }
            JSONArray members = queryMembersByDepartmentId(id);
            department.put("members", members);
        }
        buildTree(departments, maxDepartment);

        removeSomeKeys(maxDepartment, new ArrayList<>(Arrays.asList("id", "parentid", "department_leader", "order",
                "department")));

        envAndAllMembers.put(qwEnum, maxDepartment);
        return maxDepartment;
    }

    /**
     * 每10分钟刷新一下token
     */
    @Scheduled(cron = "* */10 * * * ?")
    public void clearAllMembersMap() {
        envAndAccessTokenMap.clear();
    }

    private Map<QwEnum, JSONObject> envAndAllMembers = new HashMap<>();

    private void removeSomeKeys(JSONObject currentDepartment, List<String> removeKeys) {
        removeKeys.forEach(key -> currentDepartment.remove(key));
        JSONArray members = currentDepartment.getJSONArray("members");
        if (members != null) {
            for (int i = 0; i < members.size(); i++) {
                removeSomeKeys(members.getJSONObject(i), removeKeys);
            }
        }

        JSONArray departments = currentDepartment.getJSONArray("departments");
        if (departments != null) {
            for (int i = 0; i < departments.size(); i++) {
                removeSomeKeys(departments.getJSONObject(i), removeKeys);
            }
        }
    }

    private void buildTree(JSONArray departments, JSONObject currentDepartment) {
        JSONArray childDepartments = findChildDepartment(departments, currentDepartment);
        currentDepartment.put("departments", childDepartments);
        for (int i = 0; i < childDepartments.size(); i++) {
            buildTree(departments, childDepartments.getJSONObject(i));
        }
    }

    private JSONArray findChildDepartment(JSONArray departments, JSONObject currentDepartment) {
        long id = currentDepartment.getLongValue("id");
        JSONArray childDepartments = new JSONArray();
        for (int i = 0; i < departments.size(); i++) {
            JSONObject department = departments.getJSONObject(i);
            if (id == department.getLongValue("parentid")) {
                childDepartments.add(department);
            }
        }
        return childDepartments;
    }

}
