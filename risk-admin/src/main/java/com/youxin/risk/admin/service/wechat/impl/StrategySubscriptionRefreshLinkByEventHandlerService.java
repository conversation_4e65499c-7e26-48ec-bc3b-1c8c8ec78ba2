package com.youxin.risk.admin.service.wechat.impl;

import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.utils.SubscriptionUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 根据事件代码刷新链接处理服务
 */
@Service
public class StrategySubscriptionRefreshLinkByEventHandlerService extends AbstractStrategySubscriptionHandlerService {

    @Override
    protected Object processMessage(Map<String, String> params) {
        try {
            String eventCode = getRequiredParam(params, "eventCode");

            LoggerProxy.info("processMessage", logger, "刷新监控链接, eventCode={}", eventCode);

            String refreshedLink = strategyMonitorConfigService.refreshMonitorLink(eventCode);

            if (SubscriptionUtils.isNotEmpty(refreshedLink)) {
                LoggerProxy.info("processMessage", logger,
                        "刷新监控链接成功, eventCode={}, link={}", eventCode, refreshedLink);
                return refreshedLink;
            } else {
                LoggerProxy.warn("processMessage", logger,
                        "刷新监控链接失败, eventCode={}", eventCode);
                throw new RuntimeException("刷新监控链接失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("processMessage", logger,
                    "刷新监控链接异常, params=" + params, e);
            return e;
        }
    }

    @Override
    protected String buildResponseMessage(Object result, Map<String, String> params) {
        StringBuilder markdownContent = new StringBuilder();
        markdownContent.append("# 根据事件代码刷新监控链接\n\n");
        
        String eventCode = getParam(params, "eventCode");
        markdownContent.append("> **事件代码:** ").append(infoMessage(eventCode)).append("\n");
        
        if (result instanceof String) {
            String refreshedLink = (String) result;
            markdownContent.append("> **操作结果:** ").append(successMessage("刷新成功")).append("\n");
            markdownContent.append("> **新链接:** [点击查看监控](" + refreshedLink + ")\n");
            markdownContent.append("\n> 监控链接已成功刷新，您可以点击上方链接查看最新的监控数据。");
        } else if (result instanceof Exception) {
            Exception e = (Exception) result;
            markdownContent.append("> **操作结果:** ").append(warnMessage("刷新失败")).append("\n");
            markdownContent.append("> **错误信息:** ").append(e.getMessage()).append("\n");
        }
        
        return markdownContent.toString();
    }

    @Override
    protected String supportMsgType() {
        return AlertExtendOperationTypeEnum.STRATEGY_SUBSCRIPTION_REFRESH_LINK_BY_EVENT.getCode();
    }
}
