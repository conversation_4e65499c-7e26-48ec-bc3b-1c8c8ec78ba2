package com.youxin.risk.admin.tools.wechat.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QwEnum {
    WEICAI("ww63478d6a674cf652","AaHZ4CB4ORTQLw0gKZ3AbMHbKOuZlC44WYJ4ZKLWEPc", "weicai");

    /**
     * 企业微信 微财企业ID\审批应用凭证密钥
     */
    private String corpId;
    private String corpSecret;
    private String enterpriseEnv;

    public static String getByCorpId(String corpId){
        for(QwEnum modeEnum : values()){
            if(modeEnum.corpId.equals(corpId)){
                return modeEnum.getEnterpriseEnv();
            }
        }
        return WEICAI.getEnterpriseEnv();
    }

    public static Boolean containCorpId(String corpId){
        for(QwEnum modeEnum : values()){
            if(modeEnum.corpId.equals(corpId)){
                return true;
            }
        }
        return false;
    }

    public static QwEnum getByEnterpriseEnv(String enterpriseEnv){
        for(QwEnum modeEnum : values()){
            if(modeEnum.getEnterpriseEnv().equalsIgnoreCase(enterpriseEnv)){
                return modeEnum;
            }
        }
        return WEICAI;
    }
}
