package com.youxin.risk.admin.service.subscription;

import com.youxin.risk.commons.utils.SLSLinkGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * SLSLinkGenerator 统一配置测试类
 * 重构后的版本，使用Spring测试环境而不是Mock
 *
 * <AUTHOR> Assistant
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:spring/spring-config.xml"
})
public class SLSLinkGeneratorUnifiedConfigTest {



    @Test
    public void testIsEventCodeConfiguredInUnifiedConfig_Found() {
        // 测试已知存在的事件代码
        // 注意：这个测试依赖于实际的Nacos配置
        boolean result = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("ApiVerify");

        // 验证方法不会抛异常
        // 结果取决于实际的配置情况，在测试环境中可能为false
        assertNotNull("结果不应该为null", result);
    }

    @Test
    public void testIsEventCodeConfiguredInUnifiedConfig_NotFound() {
        // 测试不存在的事件代码
        boolean result = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("NonExistentEvent");

        // 验证方法不会抛异常
        // 不存在的事件代码应该返回false
        assertFalse("不应该找到 NonExistentEvent 事件代码", result);
    }

    @Test
    public void testIsEventCodeConfiguredInUnifiedConfig_EmptyEventCode() {
        // 测试空事件代码
        boolean result1 = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("");
        boolean result2 = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig(null);

        // 验证方法不会抛异常，并且空事件代码返回false
        assertFalse("空事件代码应该返回false", result1);
        assertFalse("null事件代码应该返回false", result2);
    }

    @Test
    public void testGetShareableLinkByStrategyAndMonitor() {
        // 测试根据策略类型和监控ID生成链接
        String result = SLSLinkGenerator.getShareableLinkByStrategyAndMonitor("apiverify", "dashboard-1747904265305-468192");

        // 验证方法不会抛异常
        // 在测试环境中，实际的SLS服务可能不可用，配置也可能不存在
        // 主要验证方法能正确处理各种情况
        if (result != null) {
            assertTrue("生成的链接应该是有效URL", result.startsWith("http"));
        }
    }

    @Test
    public void testGetShareableLinkByEventCode_FromUnifiedConfig() {
        // 测试通过事件代码生成链接
        String result = SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify");

        // 验证方法不会抛异常
        // 在测试环境中，实际的SLS服务可能不可用，配置也可能不存在
        // 主要验证方法能正确处理各种情况
        if (result != null) {
            assertTrue("生成的链接应该是有效URL", result.startsWith("http"));
        }
    }

    @Test
    public void testGetShareableLinkByEventCode_InvalidEventCode() {
        // 测试无效事件代码
        String result1 = SLSLinkGenerator.getShareableLinkByEventCode("InvalidEventCode");
        String result2 = SLSLinkGenerator.getShareableLinkByEventCode("");
        String result3 = SLSLinkGenerator.getShareableLinkByEventCode(null);

        // 验证方法不会抛异常
        // 无效的事件代码应该返回null
        assertNull("无效事件代码应该返回null", result1);
        assertNull("空事件代码应该返回null", result2);
        assertNull("null事件代码应该返回null", result3);
    }

    @Test
    public void testGetShareableLinkByStrategyAndMonitor_InvalidParams() {
        // 测试无效参数
        String result1 = SLSLinkGenerator.getShareableLinkByStrategyAndMonitor("invalidStrategy", "invalidMonitor");
        String result2 = SLSLinkGenerator.getShareableLinkByStrategyAndMonitor("", "");
        String result3 = SLSLinkGenerator.getShareableLinkByStrategyAndMonitor(null, null);

        // 验证方法不会抛异常
        // 无效参数应该返回null
        assertNull("无效策略类型和监控ID应该返回null", result1);
        assertNull("空参数应该返回null", result2);
        assertNull("null参数应该返回null", result3);
    }

    @Test
    public void testSLSLinkGeneratorMethods_ExceptionHandling() {
        // 测试各种边界情况，确保方法能正确处理异常

        // 测试事件代码检查
        boolean configResult = SLSLinkGenerator.isEventCodeConfiguredInUnifiedConfig("TestEvent");
        assertNotNull("配置检查结果不应该为null", configResult);

        // 测试链接生成
        String linkResult = SLSLinkGenerator.getShareableLinkByEventCode("TestEvent");
        // 在测试环境中可能返回null，这是正常的

        // 测试策略和监控ID方式的链接生成
        String strategyResult = SLSLinkGenerator.getShareableLinkByStrategyAndMonitor("testStrategy", "testMonitor");
        // 在测试环境中可能返回null，这是正常的

        // 主要验证这些方法不会抛出未捕获的异常
        assertTrue("所有方法都应该正常执行完成", true);
    }
}
