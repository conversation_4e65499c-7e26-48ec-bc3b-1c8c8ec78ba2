package com.youxin.risk.commons.utils;

import org.apache.commons.lang.StringUtils;

/**
 * 订阅相关工具类
 * 提取可复用的订阅逻辑
 *
 */
public class SubscriptionUtils {

    /**
     * 验证订阅参数
     *
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @return 验证结果
     */
    public static boolean validateSubscriptionParams(String userId, String strategyType) {
        return StringUtils.isNotEmpty(userId) && StringUtils.isNotEmpty(strategyType);
    }

    /**
     * 验证字符串参数是否非空
     *
     * @param value 待验证的值
     * @return 验证结果
     */
    public static boolean isNotEmpty(String value) {
        return StringUtils.isNotEmpty(value);
    }

    /**
     * 验证多个字符串参数是否都非空
     *
     * @param values 待验证的值数组
     * @return 验证结果
     */
    public static boolean areAllNotEmpty(String... values) {
        if (values == null || values.length == 0) {
            return false;
        }
        for (String value : values) {
            if (StringUtils.isEmpty(value)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 验证推送配置参数
     *
     * @param frequencyMinutes 推送频率（分钟）
     * @param totalPushes 总推送次数
     * @return 验证结果
     */
    public static boolean validatePushConfig(Integer frequencyMinutes, Integer totalPushes) {
        return validateFrequencyMinutes(frequencyMinutes) && validateTotalPushes(totalPushes);
    }

    /**
     * 验证推送频率配置
     *
     * @param frequencyMinutes 推送频率（分钟）
     * @return 验证结果
     */
    public static boolean validateFrequencyMinutes(Integer frequencyMinutes) {
        return frequencyMinutes != null && frequencyMinutes > 0 && frequencyMinutes <= 1440; // 最大24小时
    }

    /**
     * 验证总推送次数配置
     *
     * @param totalPushes 总推送次数
     * @return 验证结果
     */
    public static boolean validateTotalPushes(Integer totalPushes) {
        return totalPushes != null && totalPushes > 0 && totalPushes <= 100; // 最大100次
    }

    /**
     * 获取推送频率的显示文本
     *
     * @param frequencyMinutes 推送频率（分钟）
     * @return 显示文本
     */
    public static String getFrequencyDisplayText(int frequencyMinutes) {
        if (frequencyMinutes <= 0) {
            return "无效频率";
        } else if (frequencyMinutes < 60) {
            return frequencyMinutes + "分钟";
        } else if (frequencyMinutes == 60) {
            return "1小时";
        } else if (frequencyMinutes % 60 == 0) {
            return (frequencyMinutes / 60) + "小时";
        } else {
            int hours = frequencyMinutes / 60;
            int minutes = frequencyMinutes % 60;
            return hours + "小时" + minutes + "分钟";
        }
    }

    /**
     * 构建推送配置摘要
     *
     * @param frequencyMinutes 推送频率
     * @param totalPushes 总推送次数
     * @return 配置摘要
     */
    public static String buildPushConfigSummary(int frequencyMinutes, int totalPushes) {
        return String.format("每%s推送，共%d次", getFrequencyDisplayText(frequencyMinutes), totalPushes);
    }

    /**
     * 格式化推送进度信息
     *
     * @param currentPush 当前推送次数
     * @param totalPush 总推送次数
     * @return 格式化后的字符串
     */
    public static String formatPushProgress(int currentPush, int totalPush) {
        return String.format("第%d/%d次", currentPush, totalPush);
    }

    /**
     * 检查是否需要推送
     *
     * @param sentPushes 已推送次数
     * @param totalPushes 总推送次数
     * @param lastNotifiedTime 上次通知时间（毫秒）
     * @param frequencyMinutes 推送频率（分钟）
     * @return 是否需要推送
     */
    public static boolean shouldPush(int sentPushes, int totalPushes, Long lastNotifiedTime, int frequencyMinutes) {
        // 检查推送次数
        if (sentPushes >= totalPushes) {
            return false;
        }

        // 检查时间间隔
        if (lastNotifiedTime == null) {
            return true; // 首次推送
        }

        long currentTime = System.currentTimeMillis();
        long intervalMillis = frequencyMinutes * 60 * 1000L;
        return (currentTime - lastNotifiedTime) >= intervalMillis;
    }

    /**
     * 计算下次推送时间
     *
     * @param lastNotifiedTime 上次通知时间（毫秒）
     * @param frequencyMinutes 推送频率（分钟）
     * @return 下次推送时间（毫秒）
     */
    public static long calculateNextPushTime(Long lastNotifiedTime, int frequencyMinutes) {
        if (lastNotifiedTime == null) {
            return System.currentTimeMillis();
        }
        return lastNotifiedTime + (frequencyMinutes * 60 * 1000L);
    }

    /**
     * 检查监控配置是否使用动态链接
     *
     * @param useDynamicLink 是否使用动态链接标志
     * @param eventCode 事件代码
     * @return 是否使用动态链接
     */
    public static boolean isDynamicLinkEnabled(Boolean useDynamicLink, String eventCode) {
        return Boolean.TRUE.equals(useDynamicLink) && isNotEmpty(eventCode);
    }
}
